# Implementation Guide
## Step-by-Step Kubernetes Observability Setup

### Phase 1: Prerequisites and Planning

#### 1.1 Environment Preparation
```bash
# Verify Kubernetes cluster
kubectl cluster-info
kubectl get nodes

# Check available storage classes
kubectl get storageclass

# Verify you have cluster-admin permissions
kubectl auth can-i "*" "*"
```

#### 1.2 Create Namespace
```bash
kubectl create namespace observability
kubectl label namespace observability name=observability
```

### Phase 2: Dedicated Persistent Storage Setup

#### 2.1 Create Dedicated Persistent Volumes (10 minutes)
1. **Create Pod Logs Persistent Volume (100GB)**
   ```bash
   kubectl apply -f storage/pod-logs-pv.yaml
   kubectl apply -f storage/pod-logs-pvc.yaml
   ```

2. **Create Node Logs Persistent Volume (50GB)**
   ```bash
   kubectl apply -f storage/node-logs-pv.yaml
   kubectl apply -f storage/node-logs-pvc.yaml
   ```

3. **Create Loki Storage Persistent Volume (200GB)**
   ```bash
   kubectl apply -f storage/loki-storage-pv.yaml
   kubectl apply -f storage/loki-storage-pvc.yaml
   ```

4. **Create Prometheus Storage Persistent Volume (150GB)**
   ```bash
   kubectl apply -f storage/prometheus-storage-pv.yaml
   kubectl apply -f storage/prometheus-storage-pvc.yaml
   ```

5. **Verify all PVCs are bound**
   ```bash
   kubectl get pvc -n observability
   kubectl get pv
   ```

### Phase 3: Logging Stack Implementation (Prometheus Ecosystem)

#### 3.1 Deploy Loki (15 minutes)
1. **Deploy Loki for log aggregation**
   ```bash
   kubectl apply -f logging/loki/deployment.yaml
   kubectl apply -f logging/loki/service.yaml
   ```

2. **Verify Loki is running**
   ```bash
   kubectl get pods -n observability -l app=loki
   kubectl logs -n observability -l app=loki
   ```

#### 3.2 Deploy Grafana Alloy (10 minutes)
1. **Create RBAC permissions**
   ```bash
   kubectl apply -f logging/alloy/rbac.yaml
   ```

2. **Create ConfigMap for Alloy configuration**
   ```bash
   kubectl apply -f logging/alloy/configmap.yaml
   ```

3. **Deploy Alloy DaemonSet**
   ```bash
   kubectl apply -f logging/alloy/daemonset.yaml
   ```

4. **Verify Alloy is running on all nodes**
   ```bash
   kubectl get pods -n observability -l app=alloy
   kubectl get daemonset -n observability alloy
   ```

5. **Check Alloy metrics endpoint**
   ```bash
   kubectl port-forward -n observability ds/alloy 12345:12345
   curl http://localhost:12345/-/ready
   ```

### Phase 4: Process-Level Metrics Implementation

#### 4.1 Deploy Process Exporters (15 minutes)
1. **Deploy Process Exporter Configuration**
   ```bash
   kubectl apply -f metrics/process-exporters/process-exporter-config.yaml
   ```

2. **Deploy Process Exporter DaemonSet**
   ```bash
   kubectl apply -f metrics/process-exporters/process-exporter-daemonset.yaml
   ```

3. **Verify Process Exporter is running on all nodes**
   ```bash
   kubectl get pods -n observability -l app=process-exporter
   kubectl get daemonset -n observability process-exporter
   ```

4. **Test Process Exporter metrics**
   ```bash
   kubectl port-forward -n observability ds/process-exporter 9256:9256
   curl http://localhost:9256/metrics | grep process_
   ```

#### 4.2 Deploy Prometheus (20 minutes)
1. **Create RBAC for Prometheus**
   ```bash
   kubectl apply -f metrics/prometheus/rbac.yaml
   ```

2. **Create Prometheus configuration with process metrics support**
   ```bash
   kubectl apply -f metrics/prometheus/configmap.yaml
   ```

3. **Deploy Prometheus server**
   ```bash
   kubectl apply -f metrics/prometheus/deployment.yaml
   kubectl apply -f metrics/prometheus/service.yaml
   ```

4. **Verify Prometheus is scraping all targets including process metrics**
   ```bash
   kubectl port-forward -n observability svc/prometheus 9090:9090
   # Open browser: http://localhost:9090/targets
   # Verify: kubernetes-pods-process-metrics, process-exporter, node-exporter targets
   ```

#### 3.2 Deploy Node Exporter (10 minutes)
1. **Deploy Node Exporter DaemonSet**
   ```bash
   kubectl apply -f metrics/node-exporter/daemonset.yaml
   ```

2. **Verify Node Exporter on all nodes**
   ```bash
   kubectl get pods -n observability -l app=node-exporter
   ```

#### 4.4 Deploy Grafana (15 minutes)
1. **Create Grafana configuration with Prometheus and Loki datasources**
   ```bash
   kubectl apply -f metrics/grafana/configmap.yaml
   ```

2. **Deploy Grafana**
   ```bash
   kubectl apply -f metrics/grafana/deployment.yaml
   kubectl apply -f metrics/grafana/service.yaml
   ```

3. **Access Grafana dashboard**
   ```bash
   kubectl port-forward -n observability svc/grafana 3000:3000
   # Open browser: http://localhost:3000
   # Default login: admin/admin123
   ```

4. **Verify datasources are configured**
   - Prometheus: http://prometheus:9090
   - Loki: http://loki:3100

### Phase 4: Application Metrics Integration

#### 4.1 Add Metrics to Your Applications (30 minutes per pod)
For each of your 9 pods, you need to:

1. **Add metrics library to your application**
   - For Java: Micrometer + Prometheus
   - For Node.js: prom-client
   - For Python: prometheus_client
   - For Go: prometheus/client_golang

2. **Expose metrics endpoint**
   - Add `/metrics` endpoint on port 8080
   - Include standard metrics: CPU, memory, request count, response time

3. **Update pod specifications**
   ```yaml
   # Add to your pod spec
   ports:
   - containerPort: 8080
     name: metrics
   annotations:
     prometheus.io/scrape: "true"
     prometheus.io/port: "8080"
     prometheus.io/path: "/metrics"
   ```

#### 4.2 Configure Service Discovery
Update Prometheus configuration to auto-discover your pods:
```yaml
# This is already included in the provided configmap
- job_name: 'kubernetes-pods'
  kubernetes_sd_configs:
  - role: pod
  relabel_configs:
  - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
    action: keep
    regex: true
```

### Phase 5: Azure Integration

#### 5.1 Configure Azure Monitor Integration (20 minutes)
1. **Create Azure Monitor workspace**
   ```bash
   az monitor log-analytics workspace create \
     --resource-group your-rg \
     --workspace-name k8s-observability
   ```

2. **Get workspace credentials**
   ```bash
   az monitor log-analytics workspace get-shared-keys \
     --resource-group your-rg \
     --workspace-name k8s-observability
   ```

3. **Apply Azure integration configuration**
   ```bash
   kubectl apply -f metrics/azure-integration/azure-metrics-config.yaml
   ```

### Phase 6: Monitoring and Alerting

#### 6.1 Configure ServiceMonitors (10 minutes)
```bash
kubectl apply -f monitoring/servicemonitor.yaml
```

#### 6.2 Set up Alerts (15 minutes)
```bash
kubectl apply -f monitoring/alerts.yaml
```

### Phase 7: Validation and Testing

#### 7.1 Verify Log Collection
1. Check Elasticsearch indices:
   ```bash
   curl -X GET "localhost:9200/_cat/indices?v"
   ```

2. Search logs in Kibana:
   - Create index pattern: `logstash-*`
   - Verify logs from all 9 pods are visible

#### 7.2 Verify Metrics Collection
1. Check Prometheus targets:
   - All 9 pods should be UP
   - All 3 node exporters should be UP

2. Create Grafana dashboards:
   - Import Kubernetes cluster dashboard
   - Create custom dashboard for your applications

#### 7.3 Test Azure Integration
1. Verify metrics in Azure Monitor:
   ```bash
   az monitor metrics list --resource your-resource-id
   ```

### Troubleshooting Common Issues

#### Issue 1: Fluent Bit not collecting logs
- Check DaemonSet status: `kubectl describe ds fluent-bit -n observability`
- Verify RBAC permissions
- Check Fluent Bit logs: `kubectl logs -n observability -l app=fluent-bit`

#### Issue 2: Prometheus not scraping pods
- Verify pod annotations
- Check Prometheus configuration
- Review Prometheus logs: `kubectl logs -n observability -l app=prometheus`

#### Issue 3: Elasticsearch storage issues
- Check PVC status: `kubectl get pvc -n observability`
- Verify storage class availability
- Monitor disk usage

### Next Steps
1. Set up log retention policies
2. Configure alerting rules
3. Create custom dashboards
4. Implement log parsing rules
5. Set up backup strategies

### Estimated Timeline
- **Total implementation time**: 4-6 hours
- **Phase 1-2 (Logging)**: 1-2 hours
- **Phase 3 (Metrics)**: 1-2 hours  
- **Phase 4 (App Integration)**: 2-3 hours
- **Phase 5-7 (Azure & Testing)**: 1 hour
