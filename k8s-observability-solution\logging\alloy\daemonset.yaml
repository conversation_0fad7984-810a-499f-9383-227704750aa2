apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: alloy
  namespace: observability
  labels:
    app: alloy
spec:
  selector:
    matchLabels:
      app: alloy
  template:
    metadata:
      labels:
        app: alloy
    spec:
      serviceAccountName: alloy
      containers:
      - name: alloy
        image: grafana/alloy:latest
        args:
        - run
        - /etc/alloy/config.alloy
        - --server.http.listen-addr=0.0.0.0:12345
        - --storage.path=/tmp/alloy
        env:
        - name: HOSTNAME
          valueFrom:
            fieldRef:
              fieldPath: spec.nodeName
        volumeMounts:
        - name: config
          mountPath: /etc/alloy
        - name: varlog
          mountPath: /var/log
          readOnly: true
        - name: varlibdockercontainers
          mountPath: /var/lib/docker/containers
          readOnly: true
        - name: pod-logs-storage
          mountPath: /mnt/pod-logs
        - name: node-logs-storage
          mountPath: /mnt/node-logs
        - name: tmp
          mountPath: /tmp
        ports:
        - containerPort: 12345
          name: http-metrics
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
            - ALL
          readOnlyRootFilesystem: true
        readinessProbe:
          failureThreshold: 5
          httpGet:
            path: /-/ready
            port: http-metrics
          initialDelaySeconds: 10
          periodSeconds: 10
          successThreshold: 1
          timeoutSeconds: 1
        livenessProbe:
          failureThreshold: 5
          httpGet:
            path: /-/healthy
            port: http-metrics
          initialDelaySeconds: 10
          periodSeconds: 10
      tolerations:
      - key: node-role.kubernetes.io/master
        operator: Exists
        effect: NoSchedule
      - key: node-role.kubernetes.io/control-plane
        operator: Exists
        effect: NoSchedule
      volumes:
      - name: config
        configMap:
          name: alloy-config
      - name: varlog
        hostPath:
          path: /var/log
      - name: varlibdockercontainers
        hostPath:
          path: /var/lib/docker/containers
      - name: pod-logs-storage
        persistentVolumeClaim:
          claimName: pod-logs-pvc
      - name: node-logs-storage
        persistentVolumeClaim:
          claimName: node-logs-pvc
      - name: tmp
        emptyDir: {}
