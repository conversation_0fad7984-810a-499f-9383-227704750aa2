# Template for adding process metrics sidecar to each of the 9 pods
# This should be added to each pod's deployment specification

apiVersion: v1
kind: ConfigMap
metadata:
  name: pod-metrics-sidecar-template
  namespace: observability
data:
  sidecar-template.yaml: |
    # Add this sidecar container to each of your 9 pod deployments
    - name: metrics-exporter
      image: prom/node-exporter:latest
      args:
      - --path.procfs=/host/proc
      - --path.sysfs=/host/sys
      - --path.rootfs=/host/root
      - --collector.filesystem.ignored-mount-points
      - "^/(dev|proc|sys|var/lib/docker/.+)($|/)"
      - --collector.filesystem.ignored-fs-types
      - "^(autofs|binfmt_misc|cgroup|configfs|debugfs|devpts|devtmpfs|fusectl|hugetlbfs|mqueue|overlay|proc|procfs|pstore|rpc_pipefs|securityfs|sysfs|tracefs)$"
      - --web.listen-address=:9100
      ports:
      - containerPort: 9100
        name: metrics
        protocol: TCP
      volumeMounts:
      - name: proc
        mountPath: /host/proc
        readOnly: true
      - name: sys
        mountPath: /host/sys
        readOnly: true
      - name: root
        mountPath: /host/root
        mountPropagation: HostToContainer
        readOnly: true
      resources:
        requests:
          memory: "32Mi"
          cpu: "25m"
        limits:
          memory: "64Mi"
          cpu: "50m"
      securityContext:
        runAsNonRoot: true
        runAsUser: 65534
        readOnlyRootFilesystem: true

    # Add these volumes to your pod spec
    volumes:
    - name: proc
      hostPath:
        path: /proc
    - name: sys
      hostPath:
        path: /sys
    - name: root
      hostPath:
        path: /

    # Add these annotations to your pod metadata
    annotations:
      prometheus.io/scrape: "true"
      prometheus.io/port: "9100"
      prometheus.io/path: "/metrics"
      prometheus.io/pod-name: "{{ .metadata.name }}"
      prometheus.io/namespace: "{{ .metadata.namespace }}"

  application-metrics-template.yaml: |
    # Template for application-specific metrics exporter
    # Customize this based on your application type (Java, Node.js, Python, etc.)
    
    - name: app-metrics-exporter
      image: your-app-image:latest  # Your application image
      ports:
      - containerPort: 8080
        name: app-metrics
        protocol: TCP
      env:
      - name: METRICS_ENABLED
        value: "true"
      - name: METRICS_PORT
        value: "8080"
      - name: METRICS_PATH
        value: "/metrics"
      # Add application-specific environment variables
      resources:
        requests:
          memory: "64Mi"
          cpu: "50m"
        limits:
          memory: "128Mi"
          cpu: "100m"

  custom-process-metrics.yaml: |
    # Custom process metrics collection script
    # This can be used to collect application-specific process metrics
    
    - name: custom-process-metrics
      image: busybox:latest
      command:
      - /bin/sh
      - -c
      - |
        while true; do
          # Collect process-specific metrics
          echo "# HELP process_cpu_usage_percent CPU usage percentage"
          echo "# TYPE process_cpu_usage_percent gauge"
          ps -eo pid,ppid,cmd,%cpu,%mem --no-headers | while read pid ppid cmd cpu mem; do
            echo "process_cpu_usage_percent{pid=\"$pid\",ppid=\"$ppid\",cmd=\"$cmd\"} $cpu"
            echo "process_memory_usage_percent{pid=\"$pid\",ppid=\"$ppid\",cmd=\"$cmd\"} $mem"
          done > /tmp/metrics.prom
          
          # Serve metrics on HTTP endpoint
          nc -l -p 9101 < /tmp/metrics.prom &
          sleep 30
        done
      ports:
      - containerPort: 9101
        name: custom-metrics
      resources:
        requests:
          memory: "16Mi"
          cpu: "10m"
        limits:
          memory: "32Mi"
          cpu: "25m"
