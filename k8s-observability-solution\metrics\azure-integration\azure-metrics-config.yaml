apiVersion: v1
kind: ConfigMap
metadata:
  name: azure-monitor-config
  namespace: observability
data:
  azure-config.yml: |
    # Azure Monitor Configuration for Process-Level Metrics
    
    # Azure Log Analytics Workspace Configuration
    workspace:
      id: "YOUR_WORKSPACE_ID"
      key: "YOUR_WORKSPACE_KEY"
      endpoint: "https://YOUR_WORKSPACE_ID.ods.opinsights.azure.com/api/logs?api-version=2016-04-01"
    
    # Metrics forwarding configuration
    metrics:
      # Process-level metrics to forward
      process_metrics:
        - "process_cpu_seconds_total"
        - "process_resident_memory_bytes"
        - "process_open_fds"
        - "process_max_fds"
        - "namedprocess_namegroup_num_procs"
        - "namedprocess_namegroup_cpu_seconds_total"
        - "namedprocess_namegroup_memory_bytes"
      
      # Pod-specific metrics
      pod_metrics:
        - "container_cpu_usage_seconds_total"
        - "container_memory_usage_bytes"
        - "container_network_receive_bytes_total"
        - "container_network_transmit_bytes_total"
        - "kube_pod_container_status_restarts_total"
      
      # Custom application metrics
      application_metrics:
        - "http_requests_total"
        - "http_request_duration_seconds"
        - "jvm_memory_used_bytes"
        - "db_connection_pool_active"
    
    # Log forwarding configuration
    logs:
      # Pod logs to forward
      pod_logs:
        enabled: true
        log_types:
          - "application"
          - "error"
          - "access"
      
      # Node logs to forward
      node_logs:
        enabled: true
        log_types:
          - "system"
          - "kernel"
          - "audit"
    
    # Retention and sampling
    retention:
      metrics_days: 30
      logs_days: 90
    
    sampling:
      metrics_interval: "15s"
      logs_sample_rate: 1.0

---
apiVersion: v1
kind: Secret
metadata:
  name: azure-monitor-secret
  namespace: observability
type: Opaque
data:
  # Base64 encoded Azure credentials
  # Replace with your actual encoded values
  workspace-id: "WU9VUl9XT1JLU1BBQ0VfSUQ="  # YOUR_WORKSPACE_ID
  workspace-key: "WU9VUl9XT1JLU1BBQ0VfS0VZ"  # YOUR_WORKSPACE_KEY

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: azure-prometheus-remote-write
  namespace: observability
data:
  remote-write-config.yml: |
    # Prometheus remote write configuration for Azure Monitor
    remote_write:
    - url: "https://YOUR_WORKSPACE_ID.ods.opinsights.azure.com/api/logs?api-version=2016-04-01"
      headers:
        Authorization: "SharedKey YOUR_WORKSPACE_ID:YOUR_SHARED_KEY"
        Log-Type: "PrometheusMetrics"
        time-generated-field: "TimeGenerated"
      
      # Write relabel configs for process metrics
      write_relabel_configs:
      # Keep only process-level and pod-level metrics
      - source_labels: [__name__]
        regex: 'process_.*|namedprocess_.*|container_.*|kube_pod_.*|http_.*|jvm_.*|db_.*'
        action: keep
      
      # Add Azure-specific labels
      - target_label: azure_cluster
        replacement: "kubernetes-cluster"
      - target_label: azure_environment
        replacement: "production"
      
      # Preserve pod identification
      - source_labels: [pod_name]
        target_label: azure_pod_name
      - source_labels: [namespace]
        target_label: azure_namespace
      - source_labels: [node_name]
        target_label: azure_node_name
      
      # Add process identification
      - source_labels: [groupname]
        target_label: azure_process_group
      - source_labels: [instance]
        target_label: azure_instance
      
      queue_config:
        capacity: 10000
        max_shards: 50
        min_shards: 1
        max_samples_per_send: 2000
        batch_send_deadline: 5s
        min_backoff: 30ms
        max_backoff: 100ms

---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: azure-metrics-sync
  namespace: observability
spec:
  schedule: "*/5 * * * *"  # Every 5 minutes
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: azure-sync
            image: curlimages/curl:latest
            command:
            - /bin/sh
            - -c
            - |
              # Sync process metrics to Azure Monitor
              echo "Syncing process metrics to Azure Monitor..."
              
              # Get metrics from Prometheus
              METRICS=$(curl -s "http://prometheus:9090/api/v1/query?query=up")
              
              # Format for Azure Monitor
              TIMESTAMP=$(date -u +"%Y-%m-%dT%H:%M:%S.%3NZ")
              
              # Send to Azure Monitor (placeholder - implement actual API calls)
              echo "Metrics sync completed at $TIMESTAMP"
            
            env:
            - name: AZURE_WORKSPACE_ID
              valueFrom:
                secretKeyRef:
                  name: azure-monitor-secret
                  key: workspace-id
            - name: AZURE_WORKSPACE_KEY
              valueFrom:
                secretKeyRef:
                  name: azure-monitor-secret
                  key: workspace-key
          restartPolicy: OnFailure
