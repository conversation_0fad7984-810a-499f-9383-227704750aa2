apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: process-exporter
  namespace: observability
  labels:
    app: process-exporter
spec:
  selector:
    matchLabels:
      app: process-exporter
  template:
    metadata:
      labels:
        app: process-exporter
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9256"
        prometheus.io/path: "/metrics"
    spec:
      hostPID: true
      hostNetwork: true
      containers:
      - name: process-exporter
        image: ncabatoff/process-exporter:latest
        args:
        - --procfs=/host/proc
        - --config.path=/etc/process-exporter/config.yml
        - --web.listen-address=:9256
        ports:
        - containerPort: 9256
          name: metrics
          protocol: TCP
        volumeMounts:
        - name: proc
          mountPath: /host/proc
          readOnly: true
        - name: config
          mountPath: /etc/process-exporter
        resources:
          requests:
            memory: "64Mi"
            cpu: "50m"
          limits:
            memory: "128Mi"
            cpu: "100m"
        securityContext:
          runAsNonRoot: true
          runAsUser: 65534
          readOnlyRootFilesystem: true
      volumes:
      - name: proc
        hostPath:
          path: /proc
      - name: config
        configMap:
          name: process-exporter-config
      tolerations:
      - key: node-role.kubernetes.io/master
        operator: Exists
        effect: NoSchedule
      - key: node-role.kubernetes.io/control-plane
        operator: Exists
        effect: NoSchedule
---
apiVersion: v1
kind: Service
metadata:
  name: process-exporter
  namespace: observability
  labels:
    app: process-exporter
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "9256"
spec:
  type: ClusterIP
  ports:
  - port: 9256
    targetPort: 9256
    protocol: TCP
    name: metrics
  selector:
    app: process-exporter
