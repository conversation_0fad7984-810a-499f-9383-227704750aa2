# Troubleshooting Guide

## Common Issues and Solutions

### Logging Stack Issues

#### Grafana Alloy Not Collecting Logs
**Symptoms**: No logs appearing in Loki/Grafana
**Solutions**:
1. Check DaemonSet status: `kubectl get ds alloy -n observability`
2. Verify RBAC permissions: `kubectl get clusterrolebinding alloy`
3. Check Alloy logs: `kubectl logs -n observability -l app=alloy`
4. Verify Loki connectivity: `kubectl port-forward -n observability svc/loki 3100:3100`
5. Check Alloy health: `kubectl port-forward -n observability ds/alloy 12345:12345` then `curl http://localhost:12345/-/ready`

#### Loki Storage Issues
**Symptoms**: Loki pod failing to start
**Solutions**:
1. Check PVC status: `kubectl get pvc loki-storage-pvc -n observability`
2. Verify storage class: `kubectl get storageclass`
3. Check Loki logs: `kubectl logs -n observability -l app=loki`

### Metrics Stack Issues

#### Prometheus Not Scraping Targets
**Symptoms**: Targets showing as DOWN in Prometheus
**Solutions**:
1. Verify pod annotations: `kubectl get pods -o yaml | grep prometheus.io`
2. Check Prometheus configuration: `kubectl get configmap prometheus-config -n observability -o yaml`
3. Verify network policies and firewall rules

#### Node Exporter Issues
**Symptoms**: Node metrics missing
**Solutions**:
1. Check DaemonSet status: `kubectl get ds node-exporter -n observability`
2. Verify node exporter is running on all nodes: `kubectl get pods -n observability -l app=node-exporter -o wide`

### Storage Issues

#### Persistent Volume Claims Not Binding
**Symptoms**: PVCs stuck in Pending state
**Solutions**:
1. Check available storage classes: `kubectl get storageclass`
2. Verify PV availability: `kubectl get pv`
3. Check storage provisioner logs

### Azure Integration Issues

#### Azure Monitor Connection Failed
**Symptoms**: Metrics/logs not appearing in Azure Monitor
**Solutions**:
1. Verify Azure credentials and workspace configuration
2. Check network connectivity to Azure endpoints
3. Review Azure Monitor ingestion logs

## Diagnostic Commands

### General Health Check
```bash
# Check all pods in observability namespace
kubectl get pods -n observability

# Check persistent volumes
kubectl get pv,pvc -n observability

# Check services
kubectl get svc -n observability
```

### Log Collection Diagnostics
```bash
# Check Alloy configuration
kubectl get configmap alloy-config -n observability -o yaml

# Test Alloy health
kubectl port-forward -n observability ds/alloy 12345:12345
curl http://localhost:12345/-/ready
curl http://localhost:12345/-/healthy

# Test Loki API
kubectl port-forward -n observability svc/loki 3100:3100
curl http://localhost:3100/ready
```

### Metrics Collection Diagnostics
```bash
# Check Prometheus targets
kubectl port-forward -n observability svc/prometheus 9090:9090
# Open http://localhost:9090/targets

# Check node exporter metrics
kubectl port-forward -n observability svc/node-exporter 9100:9100
curl http://localhost:9100/metrics
```

## Performance Tuning

### Storage Performance
- Use SSD storage for better I/O performance
- Monitor disk usage and plan capacity
- Implement log rotation and retention policies

### Memory Optimization
- Adjust Prometheus retention settings
- Configure Loki chunk size and retention
- Monitor pod memory usage

## Getting Help

1. Check the implementation guide: `docs/implementation-guide.md`
2. Review the technical design: `TECHNICAL_DESIGN.md`
3. Examine component logs for specific error messages
4. Verify network connectivity between components
