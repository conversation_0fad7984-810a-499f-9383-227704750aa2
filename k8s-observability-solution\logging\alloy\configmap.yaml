apiVersion: v1
kind: ConfigMap
metadata:
  name: alloy-config
  namespace: observability
data:
  config.alloy: |
    // Grafana Alloy configuration for log collection

    // Kubernetes service discovery for pods
    discovery.kubernetes "pods" {
      role = "pod"
    }

    // Kubernetes service discovery for nodes
    discovery.kubernetes "nodes" {
      role = "node"
    }

    // Log collection from Kubernetes pods
    loki.source.kubernetes "pods" {
      targets    = discovery.kubernetes.pods.targets
      forward_to = [loki.process.pod_logs.receiver]
    }

    // Process pod logs and add labels
    loki.process "pod_logs" {
      forward_to = [loki.write.loki.receiver]

      stage.docker {}

      stage.labels {
        values = {
          namespace     = "__meta_kubernetes_namespace_name",
          pod           = "__meta_kubernetes_pod_name",
          container     = "__meta_kubernetes_pod_container_name",
          node_name     = "__meta_kubernetes_pod_node_name",
          app           = "__meta_kubernetes_pod_label_app",
          component     = "__meta_kubernetes_pod_label_component",
        }
      }

      // Store logs to dedicated pod logs storage
      stage.output {
        source = "output"
      }
    }

    // File-based log collection for node logs
    loki.source.file "node_logs" {
      targets = [
        {
          __path__ = "/var/log/*.log",
          job      = "node-logs",
          node     = env("HOSTNAME"),
        },
        {
          __path__ = "/var/log/syslog",
          job      = "system-logs",
          node     = env("HOSTNAME"),
        },
      ]
      forward_to = [loki.process.node_logs.receiver]
    }

    // Process node logs
    loki.process "node_logs" {
      forward_to = [loki.write.loki.receiver]

      stage.labels {
        values = {
          job  = "job",
          node = "node",
        }
      }
    }

    // Write logs to Loki
    loki.write "loki" {
      endpoint {
        url = "http://loki:3100/loki/api/v1/push"
      }
    }

    // Optional: Metrics collection (Alloy can replace Node Exporter)
    prometheus.scrape "alloy_metrics" {
      targets = [{"__address__" = "localhost:12345"}]
      forward_to = [prometheus.remote_write.prometheus.receiver]
    }

    // Forward metrics to Prometheus
    prometheus.remote_write "prometheus" {
      endpoint {
        url = "http://prometheus:9090/api/v1/write"
      }
    }
