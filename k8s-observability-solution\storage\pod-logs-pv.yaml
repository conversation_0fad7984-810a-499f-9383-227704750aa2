apiVersion: v1
kind: PersistentVolume
metadata:
  name: pod-logs-pv
  labels:
    type: local
    component: logging
    storage-type: pod-logs
spec:
  storageClassName: manual
  capacity:
    storage: 100Gi
  accessModes:
    - ReadWriteMany
  persistentVolumeReclaimPolicy: Retain
  hostPath:
    path: "/mnt/data/pod-logs"
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: pod-logs-pvc
  namespace: observability
  labels:
    component: logging
    storage-type: pod-logs
spec:
  storageClassName: manual
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 100Gi
  selector:
    matchLabels:
      storage-type: pod-logs
