# Process-Level Metrics Implementation Guide

## Overview
This guide provides detailed instructions for implementing process-level metrics collection and pod-specific monitoring for all 9 pods in your Kubernetes cluster.

## Architecture Summary

### Three-Layer Metrics Collection
1. **Pod-Level Process Metrics**: Sidecar containers in each pod
2. **Node-Level Process Metrics**: Process Exporter DaemonSet
3. **System-Level Metrics**: Node Exporter DaemonSet

### Metrics Endpoints per Pod
- `:9100/metrics` - Pod sidecar process metrics
- `:8080/metrics` - Application-specific metrics
- `:9101/metrics` - Custom process metrics

## Implementation Steps

### Phase 1: Deploy Process Exporters (20 minutes)

#### 1.1 Deploy Process Exporter DaemonSet
```bash
# Deploy process exporter configuration
kubectl apply -f metrics/process-exporters/process-exporter-config.yaml

# Deploy process exporter DaemonSet
kubectl apply -f metrics/process-exporters/process-exporter-daemonset.yaml

# Verify deployment
kubectl get pods -n observability -l app=process-exporter
kubectl get daemonset -n observability process-exporter
```

#### 1.2 Test Process Exporter
```bash
# Port forward to test metrics
kubectl port-forward -n observability ds/process-exporter 9256:9256

# Check metrics endpoint
curl http://localhost:9256/metrics | grep process_
```

### Phase 2: Add Sidecar Metrics to Each Pod (30 minutes per pod)

#### 2.1 Modify Pod Deployments
For each of your 9 pods, add the metrics sidecar container:

```yaml
# Example for Pod 1 - repeat for all 9 pods
apiVersion: apps/v1
kind: Deployment
metadata:
  name: your-app-pod-1
  namespace: your-namespace
spec:
  template:
    metadata:
      annotations:
        # Enable Prometheus scraping
        prometheus.io/scrape: "true"
        prometheus.io/port: "9100"
        prometheus.io/path: "/metrics"
        prometheus.io/scrape_app: "true"
        prometheus.io/app_port: "8080"
        prometheus.io/app_path: "/metrics"
        prometheus.io/scrape_custom: "true"
        prometheus.io/custom_port: "9101"
        # Pod identification
        prometheus.io/pod-id: "pod-1"
        prometheus.io/pod-name: "your-app-pod-1"
    spec:
      containers:
      # Your main application container
      - name: your-app
        image: your-app:latest
        ports:
        - containerPort: 8080
          name: app-metrics
        env:
        - name: METRICS_ENABLED
          value: "true"
        
      # Add metrics sidecar container
      - name: metrics-exporter
        image: prom/node-exporter:latest
        args:
        - --path.procfs=/host/proc
        - --path.sysfs=/host/sys
        - --path.rootfs=/host/root
        - --web.listen-address=:9100
        ports:
        - containerPort: 9100
          name: metrics
        volumeMounts:
        - name: proc
          mountPath: /host/proc
          readOnly: true
        - name: sys
          mountPath: /host/sys
          readOnly: true
        - name: root
          mountPath: /host/root
          readOnly: true
        resources:
          requests:
            memory: "32Mi"
            cpu: "25m"
          limits:
            memory: "64Mi"
            cpu: "50m"
            
      # Add custom process metrics container
      - name: custom-process-metrics
        image: busybox:latest
        command:
        - /bin/sh
        - -c
        - |
          while true; do
            # Collect process-specific metrics for this pod
            echo "# HELP pod_process_count Number of processes in pod"
            echo "# TYPE pod_process_count gauge"
            echo "pod_process_count{pod_id=\"pod-1\"} $(ps aux | wc -l)"
            
            echo "# HELP pod_memory_usage_mb Memory usage in MB"
            echo "# TYPE pod_memory_usage_mb gauge"
            echo "pod_memory_usage_mb{pod_id=\"pod-1\"} $(free -m | awk 'NR==2{printf \"%.2f\", $3}')"
            
            # Add more custom metrics as needed
            sleep 30
          done | nc -l -p 9101
        ports:
        - containerPort: 9101
          name: custom-metrics
          
      volumes:
      - name: proc
        hostPath:
          path: /proc
      - name: sys
        hostPath:
          path: /sys
      - name: root
        hostPath:
          path: /
```

#### 2.2 Deploy Updated Pod Configurations
```bash
# Apply updated deployments for all 9 pods
kubectl apply -f your-app-pod-1-deployment.yaml
kubectl apply -f your-app-pod-2-deployment.yaml
# ... repeat for all 9 pods

# Verify all pods have metrics sidecars
kubectl get pods -o wide | grep your-app
```

### Phase 3: Configure Prometheus Scraping (15 minutes)

#### 3.1 Deploy Prometheus Configuration
```bash
# Apply Prometheus configuration with pod-specific scraping
kubectl apply -f metrics/prometheus/configmap.yaml

# Restart Prometheus to pick up new configuration
kubectl rollout restart deployment/prometheus -n observability
```

#### 3.2 Verify Prometheus Targets
```bash
# Port forward to Prometheus
kubectl port-forward -n observability svc/prometheus 9090:9090

# Check targets in Prometheus UI
# Open http://localhost:9090/targets
# Verify all 9 pods appear with their metrics endpoints
```

### Phase 4: Deploy Pod-Level Alerting (10 minutes)

#### 4.1 Deploy Alert Rules
```bash
# Deploy pod-specific alert rules
kubectl apply -f monitoring/pod-level-alerts.yaml

# Verify alert rules are loaded
kubectl port-forward -n observability svc/prometheus 9090:9090
# Check http://localhost:9090/rules
```

#### 4.2 Customize Alerts for Each Pod
Edit `monitoring/pod-level-alerts.yaml` and create specific alerts for each pod:

```yaml
# Example: Customize for each pod
- alert: Pod1HighCPU
  expr: rate(process_cpu_seconds_total{pod_name=~"your-app-pod-1.*"}[5m]) * 100 > 80
  for: 2m
  labels:
    severity: warning
    pod_id: "pod-1"
  annotations:
    summary: "Pod 1 has high CPU usage"

- alert: Pod2HighMemory
  expr: process_resident_memory_bytes{pod_name=~"your-app-pod-2.*"} / (1024*1024*1024) > 2
  for: 2m
  labels:
    severity: warning
    pod_id: "pod-2"
  annotations:
    summary: "Pod 2 has high memory usage"

# Repeat for all 9 pods...
```

### Phase 5: Grafana Dashboard Setup (20 minutes)

#### 5.1 Create Pod-Specific Dashboards
Create individual dashboards for each pod with process-level metrics:

```json
{
  "dashboard": {
    "title": "Pod 1 Process Metrics",
    "panels": [
      {
        "title": "Pod 1 CPU Usage by Process",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(namedprocess_namegroup_cpu_seconds_total{pod_name=~\"your-app-pod-1.*\"}[5m]) * 100"
          }
        ]
      },
      {
        "title": "Pod 1 Memory Usage by Process",
        "type": "graph",
        "targets": [
          {
            "expr": "namedprocess_namegroup_memory_bytes{pod_name=~\"your-app-pod-1.*\"}"
          }
        ]
      },
      {
        "title": "Pod 1 Process Count",
        "type": "stat",
        "targets": [
          {
            "expr": "namedprocess_namegroup_num_procs{pod_name=~\"your-app-pod-1.*\"}"
          }
        ]
      }
    ]
  }
}
```

### Phase 6: Azure Monitor Integration (15 minutes)

#### 6.1 Configure Process Metrics Export to Azure
Update the Prometheus remote write configuration to include process metrics:

```yaml
remote_write:
- url: "https://your-workspace.ods.opinsights.azure.com/api/logs?api-version=2016-04-01"
  write_relabel_configs:
  - source_labels: [__name__]
    regex: 'process_.*|namedprocess_.*|pod_.*'
    action: keep
  - source_labels: [pod_name]
    target_label: azure_pod_name
  - source_labels: [pod_id]
    target_label: azure_pod_id
```

## Validation and Testing

### Verify Process Metrics Collection
```bash
# Check if all pod metrics are being collected
kubectl port-forward -n observability svc/prometheus 9090:9090

# Query for pod-specific metrics
curl -G 'http://localhost:9090/api/v1/query' \
  --data-urlencode 'query=up{job="kubernetes-pods-process-metrics"}'

# Check process-level metrics
curl -G 'http://localhost:9090/api/v1/query' \
  --data-urlencode 'query=namedprocess_namegroup_num_procs'
```

### Test Pod-Level Alerts
```bash
# Generate high CPU load in a specific pod to test alerting
kubectl exec -it your-app-pod-1 -- stress --cpu 2 --timeout 300s

# Check if alerts fire in Prometheus
# Open http://localhost:9090/alerts
```

## Troubleshooting

### Common Issues
1. **Sidecar containers not starting**: Check resource limits and security contexts
2. **Metrics not appearing**: Verify Prometheus annotations and port configurations
3. **High cardinality**: Limit process name labels to avoid metric explosion
4. **Performance impact**: Monitor resource usage of metrics sidecars

### Debugging Commands
```bash
# Check sidecar logs
kubectl logs your-app-pod-1 -c metrics-exporter

# Test metrics endpoints
kubectl port-forward your-app-pod-1 9100:9100
curl http://localhost:9100/metrics

# Check Prometheus configuration
kubectl get configmap prometheus-config -n observability -o yaml
```

## Best Practices

1. **Resource Limits**: Set appropriate limits for metrics sidecars
2. **Metric Labels**: Use consistent labeling for pod identification
3. **Retention**: Configure appropriate retention for high-cardinality process metrics
4. **Sampling**: Consider sampling for very high-frequency process metrics
5. **Security**: Ensure metrics don't expose sensitive information

## Next Steps

1. Customize metrics collection for your specific application types
2. Create application-specific dashboards
3. Set up notification channels for pod-level alerts
4. Implement automated remediation for common issues
5. Monitor the monitoring system itself for performance
