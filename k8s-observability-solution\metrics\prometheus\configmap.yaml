apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-config
  namespace: observability
data:
  prometheus.yml: |
    global:
      scrape_interval: 15s
      evaluation_interval: 15s
      external_labels:
        cluster: 'kubernetes-cluster'
        environment: 'production'

    rule_files:
    - "/etc/prometheus/rules/*.yml"

    alerting:
      alertmanagers:
      - static_configs:
        - targets:
          - alertmanager:9093

    scrape_configs:
    # Scrape Prometheus itself
    - job_name: 'prometheus'
      static_configs:
      - targets: ['localhost:9090']

    # Scrape process-level metrics from each node
    - job_name: 'process-exporter'
      kubernetes_sd_configs:
      - role: endpoints
        namespaces:
          names:
          - observability
      relabel_configs:
      - source_labels: [__meta_kubernetes_service_name]
        action: keep
        regex: process-exporter
      - source_labels: [__meta_kubernetes_endpoint_port_name]
        action: keep
        regex: metrics
      - source_labels: [__meta_kubernetes_pod_node_name]
        target_label: node
      - source_labels: [__meta_kubernetes_pod_name]
        target_label: pod
      - source_labels: [__meta_kubernetes_namespace_name]
        target_label: namespace

    # Scrape individual pod metrics (for each of the 9 pods)
    - job_name: 'kubernetes-pods-process-metrics'
      kubernetes_sd_configs:
      - role: pod
      relabel_configs:
      # Only scrape pods with the metrics annotation
      - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
        action: keep
        regex: true
      # Use the metrics port from annotation
      - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_port]
        action: replace
        target_label: __address__
        regex: ([^:]+)(?::\d+)?;(\d+)
        replacement: $1:$2
      # Use the metrics path from annotation
      - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
        action: replace
        target_label: __metrics_path__
        regex: (.+)
      # Add pod-specific labels
      - source_labels: [__meta_kubernetes_pod_name]
        target_label: pod_name
      - source_labels: [__meta_kubernetes_namespace_name]
        target_label: namespace
      - source_labels: [__meta_kubernetes_pod_node_name]
        target_label: node_name
      - source_labels: [__meta_kubernetes_pod_label_app]
        target_label: app
      - source_labels: [__meta_kubernetes_pod_label_version]
        target_label: version
      # Create unique instance identifier for each pod
      - source_labels: [__meta_kubernetes_pod_name, __meta_kubernetes_namespace_name]
        target_label: instance
        separator: "."
        regex: (.+)
        replacement: ${1}

    # Scrape application-specific metrics from pods
    - job_name: 'kubernetes-pods-app-metrics'
      kubernetes_sd_configs:
      - role: pod
      relabel_configs:
      - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape_app]
        action: keep
        regex: true
      - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_app_port]
        action: replace
        target_label: __address__
        regex: ([^:]+)(?::\d+)?;(\d+)
        replacement: $1:$2
      - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_app_path]
        action: replace
        target_label: __metrics_path__
        regex: (.+)
      - source_labels: [__meta_kubernetes_pod_name]
        target_label: pod_name
      - source_labels: [__meta_kubernetes_namespace_name]
        target_label: namespace
      - source_labels: [__meta_kubernetes_pod_label_app]
        target_label: app
      - target_label: metrics_type
        replacement: application

    # Scrape custom process metrics
    - job_name: 'kubernetes-pods-custom-process'
      kubernetes_sd_configs:
      - role: pod
      relabel_configs:
      - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape_custom]
        action: keep
        regex: true
      - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_custom_port]
        action: replace
        target_label: __address__
        regex: ([^:]+)(?::\d+)?;(\d+)
        replacement: $1:$2
      - source_labels: [__meta_kubernetes_pod_name]
        target_label: pod_name
      - source_labels: [__meta_kubernetes_namespace_name]
        target_label: namespace
      - target_label: metrics_type
        replacement: custom_process

    # Node Exporter for node-level metrics
    - job_name: 'node-exporter'
      kubernetes_sd_configs:
      - role: endpoints
        namespaces:
          names:
          - observability
      relabel_configs:
      - source_labels: [__meta_kubernetes_service_name]
        action: keep
        regex: node-exporter
      - source_labels: [__meta_kubernetes_endpoint_port_name]
        action: keep
        regex: metrics

    # Remote write to Azure Monitor
    remote_write:
    - url: "https://your-workspace.ods.opinsights.azure.com/api/logs?api-version=2016-04-01"
      headers:
        Authorization: "SharedKey your-workspace-id:your-shared-key"
        Log-Type: "PrometheusMetrics"
      write_relabel_configs:
      - source_labels: [__name__]
        regex: 'process_.*|pod_.*|container_.*'
        action: keep
