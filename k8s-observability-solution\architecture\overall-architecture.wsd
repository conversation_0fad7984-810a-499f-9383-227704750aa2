@startuml overall-architecture
!theme plain
title Complete Kubernetes Observability Architecture

' Define the main sections
package "Kubernetes Cluster" as K8S {
    package "Nodes (3)" as NODES {
        rectangle "9 Application Pods\n+ Process Metrics Sidecars" as PODS #lightgreen
        rectangle "Grafana Alloy DaemonSet" as ALLOY #orange
        rectangle "Process Exporter DaemonSet" as PE #orange
        rectangle "Node Exporter DaemonSet" as NE #orange
        rectangle "Pod Metrics Exporters" as ME #lightblue
    }
}

package "Unified Observability Stack" as OBSERVABILITY {
    rectangle "Loki (Log Aggregation)" as LOKI #yellow
    rectangle "Prometheus (Metrics)" as PROM #yellow
    rectangle "Grafana (Visualization)" as GRAF #lightblue
}

package "Dedicated Persistent Storage" as STORAGE {
    rectangle "Pod Logs PV (100GB)" as PODPV #lightcoral
    rectangle "Node Logs PV (50GB)" as NODEPV #lightcoral
    rectangle "Loki Storage PV (200GB)" as LOKIPV #lightcoral
    rectangle "Metrics Storage PV (150GB)" as METPV #lightcoral
}

package "Cloud Integration" as CLOUD {
    rectangle "Azure Monitor" as AZURE #lightcoral
    rectangle "Azure Log Analytics" as AZLOG #lightcoral
}

' Logging flows (GREEN = Pod Logs, BLUE = Node Logs, PURPLE = Storage, ORANGE = Processing)
PODS -[#green,thickness=2]-> ALLOY : "Container logs"
NODES -[#blue,thickness=2]-> ALLOY : "Node logs"
ALLOY -[#purple,thickness=2]-> PODPV : "Pod logs storage"
ALLOY -[#purple,thickness=2]-> NODEPV : "Node logs storage"
ALLOY -[#orange,thickness=3]-> LOKI : "Parsed logs"
LOKI -[#purple,thickness=3]-> LOKIPV : "Loki storage"
LOKI -[#cyan,thickness=3]-> GRAF : "Log queries"
LOKI -[#red,thickness=2]-> AZLOG : "Forward to Azure"

' Process-level metrics flows (GREEN = Pod Metrics, BLUE = Process Data, ORANGE = System)
PODS -[#green,thickness=2]-> ME : "Pod process metrics\n(3 endpoints per pod)"
ME -[#green,thickness=2]-> PROM : "Process metrics scraping"
PODS -[#blue,thickness=3]-> PE : "Process information"
PE -[#blue,thickness=3]-> PROM : "Detailed process data"
NODES -[#orange,thickness=2]-> NE : "Node system metrics"
NE -[#orange,thickness=2]-> PROM : "Node metrics"
PROM -[#purple,thickness=4]-> METPV : "Metrics storage"
PROM -[#cyan,thickness=3]-> GRAF : "Visualization"
PROM -[#red,thickness=3]-> AZURE : "Remote write\n(process-level data)"

' Unified visualization
GRAF --> LOKI : "Log queries"
GRAF --> PROM : "Metrics queries"

' External access
actor "DevOps Team" as DEVOPS
actor "Developers" as DEVS

DEVOPS --> GRAF : "Log analysis & metrics monitoring"
DEVS --> GRAF : "Application monitoring & troubleshooting"

' Notes
note top of K8S
  **Kubernetes Cluster**
  - 3 Nodes
  - 9 Application Pods
  - DaemonSets for collection
end note

note top of OBSERVABILITY
  **Unified Observability Stack**
  - Centralized logging with Loki
  - Comprehensive metrics with Prometheus
  - Unified visualization with Grafana
  - Persistent storage for all data
end note

note top of STORAGE
  **Dedicated Persistent Storage**
  - Separate volumes for different data types
  - Optimized for performance and retention
  - Scalable storage architecture
end note

note top of CLOUD
  **Cloud Integration**
  - Azure Monitor integration
  - Long-term retention
  - Cloud-native alerting
end note

' Legend for arrow colors and thickness
legend top left
  **Data Flow Legend**
  |= Color |= Data Type |= Thickness |= Volume |
  | <color:green>Green</color> | Pod Logs/Metrics | 2 | Medium |
  | <color:blue>Blue</color> | Node/Process Data | 3 | High |
  | <color:orange>Orange</color> | System/Processing | 2-3 | Medium-High |
  | <color:purple>Purple</color> | Storage Operations | 2-4 | Variable |
  | <color:cyan>Cyan</color> | Visualization | 3 | High |
  | <color:red>Red</color> | Cloud Integration | 2-3 | Medium-High |
endlegend

@enduml
