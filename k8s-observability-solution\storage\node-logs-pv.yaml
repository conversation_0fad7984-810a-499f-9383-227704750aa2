apiVersion: v1
kind: PersistentVolume
metadata:
  name: node-logs-pv
  labels:
    type: local
    component: logging
    storage-type: node-logs
spec:
  storageClassName: manual
  capacity:
    storage: 50Gi
  accessModes:
    - ReadWriteMany
  persistentVolumeReclaimPolicy: Retain
  hostPath:
    path: "/mnt/data/node-logs"
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: node-logs-pvc
  namespace: observability
  labels:
    component: logging
    storage-type: node-logs
spec:
  storageClassName: manual
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 50Gi
  selector:
    matchLabels:
      storage-type: node-logs
