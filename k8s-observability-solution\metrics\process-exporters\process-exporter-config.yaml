apiVersion: v1
kind: ConfigMap
metadata:
  name: process-exporter-config
  namespace: observability
data:
  config.yml: |
    process_names:
      # Monitor all processes in Kubernetes pods
      - name: "{{.Comm}}"
        cmdline:
        - '.+'
        
      # Specific monitoring for common application processes
      - name: "java_apps"
        cmdline:
        - 'java'
        
      - name: "nodejs_apps"
        cmdline:
        - 'node'
        
      - name: "python_apps"
        cmdline:
        - 'python'
        
      - name: "nginx_processes"
        cmdline:
        - 'nginx'
        
      - name: "apache_processes"
        cmdline:
        - 'httpd'
        - 'apache2'
        
      - name: "database_processes"
        cmdline:
        - 'mysql'
        - 'postgres'
        - 'redis'
        - 'mongodb'
        
      # Monitor container runtime processes
      - name: "container_runtime"
        cmdline:
        - 'containerd'
        - 'dockerd'
        - 'runc'
        
      # Monitor Kubernetes system processes
      - name: "kubelet"
        cmdline:
        - 'kubelet'
        
      - name: "kube_proxy"
        cmdline:
        - 'kube-proxy'
        
    # Additional configuration for detailed process metrics
    children: true
    threads: true
    smaps: true
    recheck: true
