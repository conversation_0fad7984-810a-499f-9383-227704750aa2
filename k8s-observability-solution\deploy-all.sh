#!/bin/bash

# Kubernetes Observability Solution - Complete Deployment Script
# This script deploys the entire observability stack with process-level metrics

set -e

echo "🚀 Starting Kubernetes Observability Solution Deployment"
echo "========================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check prerequisites
check_prerequisites() {
    print_info "Checking prerequisites..."
    
    if ! command -v kubectl &> /dev/null; then
        print_error "kubectl is not installed"
        exit 1
    fi
    
    if ! kubectl cluster-info &> /dev/null; then
        print_error "Cannot connect to Kubernetes cluster"
        exit 1
    fi
    
    print_status "Prerequisites check passed"
}

# Phase 1: Create namespace and persistent storage
deploy_infrastructure() {
    print_info "Phase 1: Deploying infrastructure..."
    
    # Create namespace
    kubectl create namespace observability --dry-run=client -o yaml | kubectl apply -f -
    print_status "Observability namespace created"
    
    # Deploy persistent volumes
    kubectl apply -f storage/
    print_status "Persistent volumes deployed"
    
    # Wait for PVCs to be bound
    print_info "Waiting for PVCs to be bound..."
    kubectl wait --for=condition=Bound pvc --all -n observability --timeout=300s
    print_status "All PVCs are bound"
}

# Phase 2: Deploy logging stack
deploy_logging() {
    print_info "Phase 2: Deploying logging stack..."
    
    # Deploy Loki
    kubectl apply -f logging/loki/
    print_status "Loki deployed"
    
    # Deploy Grafana Alloy
    kubectl apply -f logging/alloy/
    print_status "Grafana Alloy deployed"
    
    # Wait for pods to be ready
    print_info "Waiting for logging pods to be ready..."
    kubectl wait --for=condition=Ready pod -l app=loki -n observability --timeout=300s
    kubectl wait --for=condition=Ready pod -l app=alloy -n observability --timeout=300s
    print_status "Logging stack is ready"
}

# Phase 3: Deploy process-level metrics
deploy_metrics() {
    print_info "Phase 3: Deploying process-level metrics stack..."
    
    # Deploy Process Exporters
    kubectl apply -f metrics/process-exporters/
    print_status "Process Exporters deployed"
    
    # Deploy Prometheus
    kubectl apply -f metrics/prometheus/
    print_status "Prometheus deployed"
    
    # Deploy Node Exporter
    kubectl apply -f metrics/node-exporter/
    print_status "Node Exporter deployed"
    
    # Deploy Grafana
    kubectl apply -f metrics/grafana/
    print_status "Grafana deployed"
    
    # Wait for pods to be ready
    print_info "Waiting for metrics pods to be ready..."
    kubectl wait --for=condition=Ready pod -l app=prometheus -n observability --timeout=300s
    kubectl wait --for=condition=Ready pod -l app=grafana -n observability --timeout=300s
    print_status "Metrics stack is ready"
}

# Phase 4: Deploy monitoring and alerting
deploy_monitoring() {
    print_info "Phase 4: Deploying monitoring and alerting..."
    
    # Deploy pod-level alerts
    kubectl apply -f monitoring/
    print_status "Pod-level alerts deployed"
    
    print_status "Monitoring and alerting configured"
}

# Phase 5: Deploy Azure integration
deploy_azure_integration() {
    print_info "Phase 5: Deploying Azure integration..."
    
    print_warning "Azure integration requires manual configuration of workspace credentials"
    print_info "Please update azure-integration/azure-metrics-config.yaml with your Azure workspace details"
    
    # Deploy Azure configuration (will need manual credential update)
    kubectl apply -f metrics/azure-integration/
    print_status "Azure integration configuration deployed"
}

# Verification phase
verify_deployment() {
    print_info "Phase 6: Verifying deployment..."
    
    # Check all pods are running
    print_info "Checking pod status..."
    kubectl get pods -n observability
    
    # Check services
    print_info "Checking services..."
    kubectl get svc -n observability
    
    # Check PVCs
    print_info "Checking persistent volumes..."
    kubectl get pvc -n observability
    
    # Check DaemonSets
    print_info "Checking DaemonSets..."
    kubectl get ds -n observability
    
    print_status "Deployment verification completed"
}

# Display access information
display_access_info() {
    print_info "🎉 Deployment completed successfully!"
    echo ""
    echo "Access Information:"
    echo "=================="
    
    echo ""
    echo "📊 Grafana Dashboard:"
    echo "   kubectl port-forward -n observability svc/grafana 3000:3000"
    echo "   URL: http://localhost:3000"
    echo "   Login: admin/admin123"
    
    echo ""
    echo "📈 Prometheus:"
    echo "   kubectl port-forward -n observability svc/prometheus 9090:9090"
    echo "   URL: http://localhost:9090"
    
    echo ""
    echo "📋 Loki Logs:"
    echo "   Access via Grafana Explore with Loki datasource"
    
    echo ""
    echo "🔍 Process Metrics:"
    echo "   kubectl port-forward -n observability ds/process-exporter 9256:9256"
    echo "   URL: http://localhost:9256/metrics"
    
    echo ""
    echo "📊 Node Metrics:"
    echo "   kubectl port-forward -n observability ds/node-exporter 9100:9100"
    echo "   URL: http://localhost:9100/metrics"
    
    echo ""
    echo "Next Steps:"
    echo "==========="
    echo "1. Configure Azure Monitor credentials in azure-integration/"
    echo "2. Add metrics sidecars to your 9 application pods"
    echo "3. Customize alerting rules for your specific applications"
    echo "4. Create custom Grafana dashboards for your pods"
    echo ""
    echo "📚 Documentation:"
    echo "   - docs/README.md (start here!)"
    echo "   - docs/overview-and-getting-started.md"
    echo "   - docs/step-by-step-deployment-guide.md"
    echo "   - docs/troubleshooting-and-diagnostics.md"
}

# Main execution
main() {
    check_prerequisites
    deploy_infrastructure
    deploy_logging
    deploy_metrics
    deploy_monitoring
    deploy_azure_integration
    verify_deployment
    display_access_info
}

# Run main function
main "$@"
