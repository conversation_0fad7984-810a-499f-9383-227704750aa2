# YAML Files Reference Guide

This document explains every YAML file in the Kubernetes Observability Solution, their purpose, and why they are needed.

## 📁 Storage Components (4 files)

### `storage/pod-logs-pv.yaml`
**Purpose**: Creates dedicated persistent storage for application logs from all 9 pods
**What it does**:
- Creates a 100GB persistent volume for pod logs
- Creates a persistent volume claim for the logging system
- Ensures pod logs are retained even if pods restart
**Why needed**: Pod logs are ephemeral by default. This provides durable storage for log analysis and compliance.

### `storage/node-logs-pv.yaml`
**Purpose**: Creates dedicated persistent storage for system and node-level logs
**What it does**:
- Creates a 50GB persistent volume for node logs
- Stores system logs, kernel logs, and audit logs
- Provides persistent storage claim for node-level logging
**Why needed**: Node logs contain critical system information needed for troubleshooting and security auditing.

### `storage/loki-storage-pv.yaml`
**Purpose**: Creates persistent storage for Loki log aggregation system
**What it does**:
- Creates a 200GB persistent volume for Loki's internal storage
- Stores log indices and chunks
- Ensures Loki data survives pod restarts
**Why needed**: Loki needs persistent storage to maintain log indices and provide fast log queries.

### `storage/prometheus-storage-pv.yaml`
**Purpose**: Creates persistent storage for Prometheus metrics database
**What it does**:
- Creates a 150GB persistent volume for metrics storage
- Stores time-series metrics data
- Ensures metrics history is preserved
**Why needed**: Prometheus stores metrics in memory and local storage. Persistent volume ensures metrics survive pod restarts.

## 📋 Logging Components (5 files)

### `logging/alloy/rbac.yaml`
**Purpose**: Creates security permissions for Grafana Alloy log collector
**What it does**:
- Creates service account for Alloy
- Grants permissions to read pods, nodes, and logs
- Allows Alloy to discover and collect logs from all sources
**Why needed**: Kubernetes security requires explicit permissions for accessing cluster resources.

### `logging/alloy/configmap.yaml`
**Purpose**: Configures how Grafana Alloy collects and processes logs
**What it does**:
- Defines log collection rules for pods and nodes
- Configures log parsing and enrichment
- Sets up forwarding to Loki and persistent storage
**Why needed**: Alloy needs configuration to know what logs to collect and how to process them.

### `logging/alloy/daemonset.yaml`
**Purpose**: Deploys Grafana Alloy on every node to collect logs
**What it does**:
- Runs Alloy pod on each of the 3 nodes
- Mounts host directories to access logs
- Connects to persistent storage volumes
**Why needed**: DaemonSet ensures log collection happens on every node where pods are running.

### `logging/loki/deployment.yaml`
**Purpose**: Deploys Loki log aggregation system
**What it does**:
- Creates Loki server for log storage and querying
- Connects to persistent storage
- Provides API for log ingestion and queries
**Why needed**: Loki aggregates logs from all sources and provides efficient log storage and querying.

### `logging/loki/service.yaml`
**Purpose**: Creates network service for Loki and its configuration
**What it does**:
- Exposes Loki on port 3100 for log ingestion
- Provides internal DNS name for other components
- Includes Loki configuration settings
**Why needed**: Other components need a stable network endpoint to send logs to Loki.

## 📈 Metrics Components (13 files)

### Process Exporters (3 files)

#### `metrics/process-exporters/process-exporter-config.yaml`
**Purpose**: Configures detailed process monitoring rules
**What it does**:
- Defines which processes to monitor (Java, Node.js, Python, etc.)
- Sets up process grouping and labeling
- Configures process tree monitoring
**Why needed**: Process exporter needs rules to identify and monitor specific application processes.

#### `metrics/process-exporters/process-exporter-daemonset.yaml`
**Purpose**: Deploys process monitoring on every node
**What it does**:
- Runs process exporter on each of the 3 nodes
- Monitors all processes running on each node
- Exposes process metrics on port 9256
**Why needed**: To collect detailed process information from every node where your 9 pods are running.

#### `metrics/process-exporters/pod-metrics-sidecar.yaml`
**Purpose**: Template for adding process metrics to each pod
**What it does**:
- Provides sidecar container templates
- Shows how to add metrics collection to your 9 pods
- Includes examples for different application types
**Why needed**: Each of your 9 pods needs individual process monitoring capabilities.

### Prometheus (4 files)

#### `metrics/prometheus/rbac.yaml`
**Purpose**: Security permissions for Prometheus metrics collection
**What it does**:
- Creates service account for Prometheus
- Grants permissions to discover and scrape metrics
- Allows access to pods, services, and endpoints
**Why needed**: Prometheus needs permissions to automatically discover and collect metrics from all pods.

#### `metrics/prometheus/configmap.yaml`
**Purpose**: Configures Prometheus to collect process-level metrics
**What it does**:
- Sets up scraping for all 9 pods individually
- Configures process exporter metrics collection
- Sets up Azure Monitor remote write
- Defines pod-specific labeling
**Why needed**: Prometheus needs detailed configuration to collect metrics from each pod and process.

#### `metrics/prometheus/deployment.yaml`
**Purpose**: Deploys Prometheus server with persistent storage
**What it does**:
- Creates Prometheus server instance
- Connects to persistent storage volume
- Loads alerting rules for pod monitoring
**Why needed**: Prometheus server is the central metrics collection and storage system.

#### `metrics/prometheus/service.yaml`
**Purpose**: Network services for Prometheus access
**What it does**:
- Internal service for other components to query metrics
- External LoadBalancer for admin access
- Exposes Prometheus UI on port 9090
**Why needed**: Grafana and other components need to query Prometheus for metrics data.

### Grafana (3 files)

#### `metrics/grafana/configmap.yaml`
**Purpose**: Configures Grafana with data sources and settings
**What it does**:
- Connects Grafana to Prometheus for metrics
- Connects Grafana to Loki for logs
- Sets up unified observability dashboard
**Why needed**: Grafana needs to know where to get metrics and logs data for visualization.

#### `metrics/grafana/deployment.yaml`
**Purpose**: Deploys Grafana visualization platform
**What it does**:
- Creates Grafana server instance
- Sets up admin credentials
- Loads configuration for dashboards
**Why needed**: Grafana provides the unified interface for viewing logs and metrics.

#### `metrics/grafana/service.yaml`
**Purpose**: Network services for Grafana access
**What it does**:
- Internal service for cluster access
- External LoadBalancer for user access
- Exposes Grafana UI on port 3000
**Why needed**: Users need network access to view dashboards and analyze data.

### Node Exporter (1 file)

#### `metrics/node-exporter/daemonset.yaml`
**Purpose**: Collects system-level metrics from all nodes
**What it does**:
- Runs on each of the 3 nodes
- Collects CPU, memory, disk, network metrics
- Provides infrastructure monitoring
**Why needed**: System-level metrics complement process-level metrics for complete monitoring.

### Azure Integration (1 file)

#### `metrics/azure-integration/azure-metrics-config.yaml`
**Purpose**: Configures integration with Azure Monitor
**What it does**:
- Sets up Azure Monitor workspace connection
- Configures metrics forwarding to Azure
- Sets up log forwarding to Azure Log Analytics
- Includes sync job for cloud integration
**Why needed**: Provides cloud-native monitoring and long-term data retention in Azure.

## 🔍 Monitoring Components (1 file)

### `monitoring/pod-level-alerts.yaml`
**Purpose**: Defines alerting rules for individual pod monitoring
**What it does**:
- Creates alerts for each of the 9 pods individually
- Monitors process-level metrics (CPU, memory, file descriptors)
- Sets up application-specific alerts (Java heap, database connections)
- Defines resource limit alerts
**Why needed**: Proactive monitoring requires alerts when pods or processes have issues.

## 🚀 Deployment Components (1 file)

### `deploy-all.sh`
**Purpose**: Automated deployment script for the entire solution
**What it does**:
- Deploys all components in the correct order
- Waits for dependencies to be ready
- Verifies successful deployment
- Provides access information
**Why needed**: Simplifies deployment and ensures components are deployed in the right sequence.

## 📊 File Dependencies

```
Storage PVs → Loki & Prometheus → Grafana → Monitoring
     ↓              ↓                ↓
   Alloy    →    Process Exporters → Azure Integration
```

## 🎯 Why Each Component is Essential

| Component | Purpose | Impact if Missing |
|-----------|---------|-------------------|
| **Storage PVs** | Data persistence | Lose all logs/metrics on restart |
| **Alloy** | Log collection | No log data available |
| **Loki** | Log aggregation | Cannot search or analyze logs |
| **Process Exporters** | Process monitoring | No process-level visibility |
| **Prometheus** | Metrics storage | No metrics data or alerting |
| **Grafana** | Visualization | No unified dashboard |
| **Node Exporter** | System metrics | Missing infrastructure data |
| **Azure Integration** | Cloud monitoring | No cloud-native features |
| **Alerts** | Proactive monitoring | Issues go unnoticed |

## 🔧 Customization Points

- **Storage sizes**: Adjust PV sizes based on your data volume
- **Alert thresholds**: Modify alert rules for your specific requirements
- **Process monitoring**: Customize process exporter config for your applications
- **Azure credentials**: Update Azure integration with your workspace details
- **Resource limits**: Adjust CPU/memory limits based on your cluster capacity

This comprehensive setup ensures complete observability for your 9 pods with process-level monitoring, persistent storage, and cloud integration.
