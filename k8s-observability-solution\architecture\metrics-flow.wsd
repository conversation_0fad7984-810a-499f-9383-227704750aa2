@startuml metrics-flow
!theme plain
title Kubernetes Metrics Collection and Export Flow

' Define Kubernetes components
package "Kubernetes Cluster" {
    rectangle "Node 1" as N1 #lightblue {
        rectangle "Pod 1\n+ Process Metrics\n+ App Metrics\n+ Sidecar Exporter" as P1 #lightgreen
        rectangle "Pod 2\n+ Process Metrics\n+ App Metrics\n+ Sidecar Exporter" as P2 #lightgreen
        rectangle "Pod 3\n+ Process Metrics\n+ App Metrics\n+ Sidecar Exporter" as P3 #lightgreen
        rectangle "Process Exporter\n(DaemonSet)" as PE1 #orange
        rectangle "Node Exporter" as NE1 #orange
    }

    rectangle "Node 2" as N2 #lightblue {
        rectangle "Pod 4\n+ Process Metrics\n+ App Metrics\n+ Sidecar Exporter" as P4 #lightgreen
        rectangle "Pod 5\n+ Process Metrics\n+ App Metrics\n+ Sidecar Exporter" as P5 #lightgreen
        rectangle "Pod 6\n+ Process Metrics\n+ App Metrics\n+ Sidecar Exporter" as P6 #lightgreen
        rectangle "Process Exporter\n(DaemonSet)" as PE2 #orange
        rectangle "Node Exporter" as NE2 #orange
    }

    rectangle "Node 3" as N3 #lightblue {
        rectangle "Pod 7\n+ Process Metrics\n+ App Metrics\n+ Sidecar Exporter" as P7 #lightgreen
        rectangle "Pod 8\n+ Process Metrics\n+ App Metrics\n+ Sidecar Exporter" as P8 #lightgreen
        rectangle "Pod 9\n+ Process Metrics\n+ App Metrics\n+ Sidecar Exporter" as P9 #lightgreen
        rectangle "Process Exporter\n(DaemonSet)" as PE3 #orange
        rectangle "Node Exporter" as NE3 #orange
    }
}

' Metrics collection and storage
rectangle "Prometheus\nServer" as PROM #yellow
rectangle "Grafana\nDashboard" as GRAF #lightblue
rectangle "Azure Monitor\nMetrics" as AZURE #lightcoral

' Dedicated Persistent Storage
package "Persistent Storage" {
    rectangle "Prometheus Storage PV\n(150GB)" as METPV #lightcoral
}

' Process-level metrics flows from pods to Prometheus (GREEN = Pod Metrics)
P1 -[#green,thickness=2]-> PROM : "Pod 1 Process Metrics\n:9100/metrics (sidecar)\n:8080/metrics (app)\n:9101/metrics (custom)"
P2 -[#green,thickness=2]-> PROM : "Pod 2 Process Metrics\n:9100/metrics (sidecar)\n:8080/metrics (app)\n:9101/metrics (custom)"
P3 -[#green,thickness=2]-> PROM : "Pod 3 Process Metrics\n:9100/metrics (sidecar)\n:8080/metrics (app)\n:9101/metrics (custom)"
P4 -[#green,thickness=2]-> PROM : "Pod 4 Process Metrics\n:9100/metrics (sidecar)\n:8080/metrics (app)\n:9101/metrics (custom)"
P5 -[#green,thickness=2]-> PROM : "Pod 5 Process Metrics\n:9100/metrics (sidecar)\n:8080/metrics (app)\n:9101/metrics (custom)"
P6 -[#green,thickness=2]-> PROM : "Pod 6 Process Metrics\n:9100/metrics (sidecar)\n:8080/metrics (app)\n:9101/metrics (custom)"
P7 -[#green,thickness=2]-> PROM : "Pod 7 Process Metrics\n:9100/metrics (sidecar)\n:8080/metrics (app)\n:9101/metrics (custom)"
P8 -[#green,thickness=2]-> PROM : "Pod 8 Process Metrics\n:9100/metrics (sidecar)\n:8080/metrics (app)\n:9101/metrics (custom)"
P9 -[#green,thickness=2]-> PROM : "Pod 9 Process Metrics\n:9100/metrics (sidecar)\n:8080/metrics (app)\n:9101/metrics (custom)"

' Process Exporter metrics to Prometheus (BLUE = Process Details)
PE1 -[#blue,thickness=3]-> PROM : "Node 1 Process Details\n:9256/metrics"
PE2 -[#blue,thickness=3]-> PROM : "Node 2 Process Details\n:9256/metrics"
PE3 -[#blue,thickness=3]-> PROM : "Node 3 Process Details\n:9256/metrics"

' Node metrics to Prometheus (ORANGE = System Metrics)
NE1 -[#orange,thickness=2]-> PROM : "Node 1 System Metrics\n:9100/metrics"
NE2 -[#orange,thickness=2]-> PROM : "Node 2 System Metrics\n:9100/metrics"
NE3 -[#orange,thickness=2]-> PROM : "Node 3 System Metrics\n:9100/metrics"

' Prometheus to storage, visualization and Azure (PURPLE = Data Flow)
PROM -[#purple,thickness=4]-> METPV : "Persistent metrics storage"
PROM -[#cyan,thickness=3]-> GRAF : "PromQL queries\nfor visualization"
PROM -[#red,thickness=3]-> AZURE : "Remote write\nto Azure Monitor"

' Direct Azure integration (alternative path) (RED = Cloud Integration)
P1 -[#red,dashed]-> AZURE : "Direct export\n(optional)"
P2 -[#red,dashed]-> AZURE : "Direct export\n(optional)"
P3 -[#red,dashed]-> AZURE : "Direct export\n(optional)"

' Notes
note right of P1
  **Pod-Level Process Metrics:**
  - Individual process CPU/Memory
  - Process count and state
  - File descriptor usage
  - Network connections per process
  - Application-specific metrics
  - Custom business metrics
  - Container resource usage
end note

note right of PE1
  **Process Exporter Metrics:**
  - Detailed process information
  - Process tree relationships
  - Process start times
  - Command line arguments
  - Process resource consumption
end note

note right of NE1
  **Node System Metrics:**
  - Overall CPU utilization
  - System memory usage
  - Disk I/O statistics
  - Network traffic
  - File system usage
  - Kernel metrics
end note

note right of PROM
  **Prometheus Server:**
  - Scrapes metrics endpoints
  - Time-series storage
  - PromQL query language
  - Alerting rules
  - Remote write to Azure
  - Process-level metric support
end note

note right of GRAF
  **Unified Visualization:**
  - Metrics visualization
  - Pod-specific dashboards
  - Process-level monitoring
  - Individual pod alerting
  - Multi-datasource support
end note

note right of AZURE
  **Cloud Integration:**
  - Cloud-native metrics
  - Process-level data forwarding
  - Long-term retention
  - Azure alerting
  - Integration with Azure services
end note

note right of METPV
  **Dedicated Metrics Storage:**
  - 150GB persistent volume
  - High-performance storage
  - Metrics retention
  - Backup enabled
  - Optimized for time-series data
end note

' Legend for metrics flow
legend bottom right
  **Metrics Flow Legend**
  |= Color |= Data Type |= Thickness |
  | <color:green>Green</color> | Pod Metrics | 2 |
  | <color:blue>Blue</color> | Process Details | 3 |
  | <color:orange>Orange</color> | System Metrics | 2 |
  | <color:purple>Purple</color> | Storage Ops | 4 |
  | <color:cyan>Cyan</color> | Visualization | 3 |
  | <color:red>Red</color> | Cloud Export | 3 |
  | Dashed | Optional Path | - |
endlegend

@enduml
