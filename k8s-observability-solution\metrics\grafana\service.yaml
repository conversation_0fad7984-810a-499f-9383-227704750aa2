apiVersion: v1
kind: Service
metadata:
  name: grafana
  namespace: observability
  labels:
    app: grafana
spec:
  type: ClusterIP
  ports:
  - port: 3000
    targetPort: 3000
    protocol: TCP
    name: grafana
  selector:
    app: grafana
---
apiVersion: v1
kind: Service
metadata:
  name: grafana-external
  namespace: observability
  labels:
    app: grafana
spec:
  type: LoadBalancer
  ports:
  - port: 3000
    targetPort: 3000
    protocol: TCP
    name: grafana
  selector:
    app: grafana
