apiVersion: v1
kind: ConfigMap
metadata:
  name: pod-level-alert-rules
  namespace: observability
data:
  pod-alerts.yml: |
    groups:
    - name: pod-level-process-alerts
      rules:
      # High CPU usage per pod
      - alert: PodHighCPUUsage
        expr: rate(process_cpu_seconds_total[5m]) * 100 > 80
        for: 2m
        labels:
          severity: warning
          component: pod-monitoring
        annotations:
          summary: "High CPU usage detected in pod {{ $labels.pod_name }}"
          description: "Pod {{ $labels.pod_name }} in namespace {{ $labels.namespace }} has CPU usage above 80% for more than 2 minutes. Current value: {{ $value }}%"

      # High memory usage per pod
      - alert: PodHighMemoryUsage
        expr: (process_resident_memory_bytes / (1024*1024*1024)) > 1
        for: 2m
        labels:
          severity: warning
          component: pod-monitoring
        annotations:
          summary: "High memory usage detected in pod {{ $labels.pod_name }}"
          description: "Pod {{ $labels.pod_name }} in namespace {{ $labels.namespace }} is using more than 1GB of memory. Current value: {{ $value }}GB"

      # Process count alerts per pod
      - alert: PodHighProcessCount
        expr: namedprocess_namegroup_num_procs > 50
        for: 5m
        labels:
          severity: warning
          component: pod-monitoring
        annotations:
          summary: "High process count in pod {{ $labels.pod_name }}"
          description: "Pod {{ $labels.pod_name }} has more than 50 processes running. Current count: {{ $value }}"

      # File descriptor usage per pod
      - alert: PodHighFileDescriptorUsage
        expr: (process_open_fds / process_max_fds) * 100 > 80
        for: 2m
        labels:
          severity: critical
          component: pod-monitoring
        annotations:
          summary: "High file descriptor usage in pod {{ $labels.pod_name }}"
          description: "Pod {{ $labels.pod_name }} is using more than 80% of available file descriptors. Current usage: {{ $value }}%"

      # Pod restart alerts
      - alert: PodFrequentRestarts
        expr: rate(kube_pod_container_status_restarts_total[15m]) * 60 * 15 > 0
        for: 0m
        labels:
          severity: warning
          component: pod-monitoring
        annotations:
          summary: "Pod {{ $labels.pod }} is restarting frequently"
          description: "Pod {{ $labels.pod }} in namespace {{ $labels.namespace }} has restarted {{ $value }} times in the last 15 minutes"

    - name: individual-pod-alerts
      rules:
      # Individual alerts for each of the 9 pods (template - customize per pod)
      - alert: Pod1ProcessDown
        expr: up{job="kubernetes-pods-process-metrics", pod_name=~"pod-1.*"} == 0
        for: 1m
        labels:
          severity: critical
          pod_id: "pod-1"
          component: individual-pod-monitoring
        annotations:
          summary: "Pod 1 process metrics collection is down"
          description: "Unable to collect process metrics from Pod 1. The pod may be down or the metrics exporter is not responding."

      - alert: Pod1HighErrorRate
        expr: rate(http_requests_total{pod_name=~"pod-1.*", status=~"5.."}[5m]) / rate(http_requests_total{pod_name=~"pod-1.*"}[5m]) * 100 > 5
        for: 2m
        labels:
          severity: warning
          pod_id: "pod-1"
          component: individual-pod-monitoring
        annotations:
          summary: "Pod 1 has high error rate"
          description: "Pod 1 error rate is {{ $value }}% which is above the 5% threshold"

      # Template for additional pods (Pod 2-9)
      # Duplicate and modify the above rules for pod-2, pod-3, etc.

    - name: process-specific-alerts
      rules:
      # Java application specific alerts
      - alert: JavaHeapMemoryHigh
        expr: jvm_memory_used_bytes{area="heap"} / jvm_memory_max_bytes{area="heap"} * 100 > 85
        for: 2m
        labels:
          severity: warning
          component: java-monitoring
        annotations:
          summary: "Java heap memory usage is high in pod {{ $labels.pod_name }}"
          description: "Java heap memory usage is {{ $value }}% in pod {{ $labels.pod_name }}"

      # Database connection alerts
      - alert: DatabaseConnectionPoolHigh
        expr: db_connection_pool_active / db_connection_pool_max * 100 > 80
        for: 2m
        labels:
          severity: warning
          component: database-monitoring
        annotations:
          summary: "Database connection pool usage is high in pod {{ $labels.pod_name }}"
          description: "Database connection pool usage is {{ $value }}% in pod {{ $labels.pod_name }}"

      # Custom application metrics alerts
      - alert: ApplicationResponseTimeHigh
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 2
        for: 2m
        labels:
          severity: warning
          component: application-monitoring
        annotations:
          summary: "Application response time is high in pod {{ $labels.pod_name }}"
          description: "95th percentile response time is {{ $value }}s in pod {{ $labels.pod_name }}"

    - name: pod-resource-alerts
      rules:
      # Container resource limit alerts
      - alert: PodNearCPULimit
        expr: (rate(container_cpu_usage_seconds_total[5m]) / container_spec_cpu_quota * container_spec_cpu_period) * 100 > 90
        for: 2m
        labels:
          severity: warning
          component: resource-monitoring
        annotations:
          summary: "Pod {{ $labels.pod }} is near CPU limit"
          description: "Pod {{ $labels.pod }} is using {{ $value }}% of its CPU limit"

      - alert: PodNearMemoryLimit
        expr: (container_memory_usage_bytes / container_spec_memory_limit_bytes) * 100 > 90
        for: 2m
        labels:
          severity: warning
          component: resource-monitoring
        annotations:
          summary: "Pod {{ $labels.pod }} is near memory limit"
          description: "Pod {{ $labels.pod }} is using {{ $value }}% of its memory limit"
