apiVersion: v1
kind: PersistentVolume
metadata:
  name: loki-storage-pv
  labels:
    type: local
    component: logging
    storage-type: loki-storage
spec:
  storageClassName: manual
  capacity:
    storage: 200Gi
  accessModes:
    - ReadWriteOnce
  persistentVolumeReclaimPolicy: Retain
  hostPath:
    path: "/mnt/data/loki-storage"
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: loki-storage-pvc
  namespace: observability
  labels:
    component: logging
    storage-type: loki-storage
spec:
  storageClassName: manual
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 200Gi
  selector:
    matchLabels:
      storage-type: loki-storage
