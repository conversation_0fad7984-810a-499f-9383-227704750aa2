apiVersion: v1
kind: PersistentVolume
metadata:
  name: prometheus-storage-pv
  labels:
    type: local
    component: metrics
    storage-type: prometheus-storage
spec:
  storageClassName: manual
  capacity:
    storage: 150Gi
  accessModes:
    - ReadWriteOnce
  persistentVolumeReclaimPolicy: Retain
  hostPath:
    path: "/mnt/data/prometheus-storage"
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: prometheus-storage-pvc
  namespace: observability
  labels:
    component: metrics
    storage-type: prometheus-storage
spec:
  storageClassName: manual
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 150Gi
  selector:
    matchLabels:
      storage-type: prometheus-storage
