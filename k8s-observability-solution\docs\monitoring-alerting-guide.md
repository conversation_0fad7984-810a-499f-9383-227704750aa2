# Pod-Level Monitoring & Alerting Configuration Guide

This document explains the monitoring and alerting configurations for comprehensive process-level metrics monitoring.

## 📁 Monitoring Folder Structure

```
monitoring/
├── pod-level-alerts.yaml    # Individual pod alerting rules
└── [Future components]      # ServiceMonitors, dashboards, etc.
```

## ✅ Completed Components

### Pod-Level Alerting Rules
- `pod-level-alerts.yaml` - Complete alerting rules including:
  - **Individual Pod Alerts**: Specific alerts for each of the 9 pods
  - **Process-Level Alerts**: CPU, memory, file descriptors per process
  - **Resource Limit Alerts**: Pod-specific resource usage monitoring
  - **Application-Specific Alerts**: Java heap, database connections, response times
  - **Process Count Alerts**: Monitor process spawning and termination
  - **Custom Metrics Alerts**: Business logic and application-specific thresholds

## Alert Categories

### Process-Level Alerts
- `PodHighCPUUsage` - CPU usage per pod above 80%
- `PodHighMemoryUsage` - Memory usage per pod above limits
- `PodHighProcessCount` - Too many processes in a pod
- `PodHighFileDescriptorUsage` - File descriptor exhaustion
- `PodFrequentRestarts` - Pod restart frequency monitoring

### Individual Pod Alerts (Template for 9 Pods)
- `Pod1ProcessDown` - Pod 1 specific monitoring
- `Pod1HighErrorRate` - Pod 1 application error rate
- `Pod2ProcessDown` - Pod 2 specific monitoring
- `Pod2HighErrorRate` - Pod 2 application error rate
- ... (Customize for all 9 pods)

### Application-Specific Alerts
- `JavaHeapMemoryHigh` - Java application heap monitoring
- `DatabaseConnectionPoolHigh` - Database connection monitoring
- `ApplicationResponseTimeHigh` - Response time monitoring

### Resource Alerts
- `PodNearCPULimit` - Pod approaching CPU limits
- `PodNearMemoryLimit` - Pod approaching memory limits

## 🔄 Components to be Created

### Service Monitors
- `servicemonitor.yaml` - Prometheus ServiceMonitor for process metrics auto-discovery

### Grafana Dashboards
- `dashboards/pod-overview.json` - Overview of all 9 pods
- `dashboards/pod-1-details.json` - Detailed Pod 1 process metrics
- `dashboards/pod-2-details.json` - Detailed Pod 2 process metrics
- ... (Individual dashboards for all 9 pods)
- `dashboards/process-analysis.json` - Cross-pod process analysis
- `dashboards/resource-utilization.json` - Resource usage across pods

### Alert Manager Configuration
- `alertmanager.yaml` - Alert routing and notification configuration
- `notification-channels.yaml` - Slack, email, webhook configurations

## Quick Deployment
```bash
# Deploy pod-level alerts
kubectl apply -f monitoring/pod-level-alerts.yaml

# Verify alerts are loaded in Prometheus
kubectl port-forward -n observability svc/prometheus 9090:9090
# Check http://localhost:9090/rules
```

## Customization Guide

### Adding Alerts for New Pods
1. Copy the Pod1 alert template in `pod-level-alerts.yaml`
2. Replace `pod-1` with your pod identifier
3. Adjust thresholds based on pod requirements
4. Apply the updated configuration

### Custom Process Metrics Alerts
```yaml
- alert: CustomProcessMetric
  expr: your_custom_metric{pod_name=~"your-pod.*"} > threshold
  for: 2m
  labels:
    severity: warning
    pod_id: "your-pod"
  annotations:
    summary: "Custom metric alert for {{ $labels.pod_name }}"
```

## Alert Severity Levels

| Severity | Description | Response Time |
|----------|-------------|---------------|
| **Critical** | Service down, data loss risk | Immediate (< 5 min) |
| **Warning** | Performance degradation | Within 30 minutes |
| **Info** | Informational, no action needed | Next business day |

## Alert Routing Strategy

```
Critical Alerts → PagerDuty + Slack + Email
Warning Alerts → Slack + Email
Info Alerts → Email only
```

## Testing Alerts

### Generate Test Load
```bash
# Test CPU alert
kubectl exec -it your-app-pod-1 -- stress --cpu 2 --timeout 300s

# Test memory alert
kubectl exec -it your-app-pod-1 -- stress --vm 1 --vm-bytes 1G --timeout 300s

# Check if alerts fire
kubectl port-forward -n observability svc/prometheus 9090:9090
# Open http://localhost:9090/alerts
```

## Implementation Status
✅ **Pod-Level Alerts**: Complete with individual pod monitoring
🔄 **Service Monitors**: To be created
🔄 **Grafana Dashboards**: To be created
🔄 **Alert Manager**: To be created

## Best Practices

1. **Alert Fatigue Prevention**: Set appropriate thresholds to avoid noise
2. **Runbook Links**: Include troubleshooting links in alert annotations
3. **Escalation Policies**: Define clear escalation paths for different severities
4. **Regular Review**: Review and tune alert thresholds based on historical data
5. **Documentation**: Keep alert descriptions clear and actionable

## Integration with External Systems

### Slack Integration
```yaml
slack_configs:
- api_url: 'YOUR_SLACK_WEBHOOK_URL'
  channel: '#alerts'
  title: 'Pod Alert: {{ .GroupLabels.alertname }}'
  text: '{{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'
```

### Email Integration
```yaml
email_configs:
- to: '<EMAIL>'
  subject: 'Alert: {{ .GroupLabels.alertname }}'
  body: '{{ range .Alerts }}{{ .Annotations.description }}{{ end }}'
```

Refer to `docs/process-metrics-implementation.md` for detailed setup instructions.
