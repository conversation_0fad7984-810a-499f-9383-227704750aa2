# Migration Notice: Promtail → <PERSON><PERSON> Alloy

## Important Update

**⚠️ Promtail Deprecation Notice**

Prom<PERSON> has been deprecated and is in Long-Term Support (LTS) through **February 28, 2026**. Promtail will reach End-of-Life (EOL) on **March 2, 2026**.

## Solution Update

Our observability solution has been **updated to use Grafana Alloy** instead of the deprecated Promtail.

### What Changed

| Component | Before | After | Status |
|-----------|--------|-------|--------|
| **Log Collector** | Promtail | <PERSON>ana Alloy | ✅ Updated |
| **Configuration** | YAML format | Alloy format | ✅ Updated |
| **Image** | `grafana/promtail:2.9.0` | `grafana/alloy:latest` | ✅ Updated |
| **Port** | 3101 | 12345 | ✅ Updated |
| **Health Checks** | `/ready` | `/-/ready`, `/-/healthy` | ✅ Updated |

### Why <PERSON><PERSON>?

**<PERSON><PERSON>** is the modern replacement for Promtail with several advantages:

1. **Future-Proof**: Actively developed and maintained
2. **Unified Collector**: Can collect both logs AND metrics (can replace Node Exporter)
3. **Better Performance**: More efficient resource usage
4. **Enhanced Features**: Better Kubernetes integration
5. **OpenTelemetry Compatible**: Supports OTel standards
6. **Flexible Configuration**: More powerful configuration language

### Migration Benefits

- **No Functional Changes**: Same log collection capabilities
- **Better Performance**: More efficient resource usage
- **Future Support**: Long-term support and development
- **Enhanced Capabilities**: Can also collect metrics if needed
- **Simplified Stack**: Potential to replace multiple components

### Files Updated

```
k8s-observability-solution/
├── logging/
│   └── alloy/                        # Renamed from promtail/
│       ├── configmap.yaml           # Updated to Alloy format
│       ├── daemonset.yaml           # Updated image and configuration
│       └── rbac.yaml                # Updated service account name
├── architecture/
│   ├── logging-flow.wsd             # Updated diagrams
│   └── overall-architecture.wsd     # Updated diagrams
├── docs/
│   ├── implementation-guide.md      # Updated instructions
│   └── troubleshooting.md           # Updated troubleshooting
├── TECHNICAL_DESIGN.md              # Updated design document
└── README.md                        # Updated component list
```

### Configuration Changes

#### Before (Promtail YAML)
```yaml
server:
  http_listen_port: 3101
clients:
  - url: http://loki:3100/loki/api/v1/push
scrape_configs:
  - job_name: kubernetes-pods
    kubernetes_sd_configs:
      - role: pod
```

#### After (Alloy Configuration)
```alloy
discovery.kubernetes "pods" {
  role = "pod"
}

loki.source.kubernetes "pods" {
  targets    = discovery.kubernetes.pods.targets
  forward_to = [loki.write.loki.receiver]
}

loki.write "loki" {
  endpoint {
    url = "http://loki:3100/loki/api/v1/push"
  }
}
```

### Deployment Changes

#### Before
```bash
kubectl apply -f logging/promtail/
kubectl get pods -l app=promtail
```

#### After
```bash
kubectl apply -f logging/alloy/
kubectl get pods -l app=alloy
```

### Health Check Changes

#### Before
```bash
curl http://localhost:3101/ready
```

#### After
```bash
curl http://localhost:12345/-/ready
curl http://localhost:12345/-/healthy
```

### Timeline

- **Now**: Use Grafana Alloy (recommended)
- **February 28, 2026**: Promtail LTS ends
- **March 2, 2026**: Promtail EOL

### Action Required

✅ **No Action Required** - The solution has been updated to use Grafana Alloy.

If you were previously using Promtail, consider migrating to this updated solution before the EOL date.

### Additional Resources

- [Grafana Alloy Documentation](https://grafana.com/docs/alloy/)
- [Migration Guide from Promtail to Alloy](https://grafana.com/docs/alloy/latest/tasks/migrate/from-promtail/)
- [Alloy Configuration Reference](https://grafana.com/docs/alloy/latest/reference/)

### Support

For questions about this migration or the updated solution:
1. Review the updated `docs/implementation-guide.md`
2. Check `docs/troubleshooting.md` for common issues
3. Refer to the `TECHNICAL_DESIGN.md` for architecture details
