@startuml logging-flow
!theme plain
title Kubernetes Logging Architecture Flow (Prometheus Stack)

' Define Kubernetes components
package "Kubernetes Cluster" {
    rectangle "Node 1" as N1 #lightblue {
        rectangle "Pod 1" as P1 #lightgreen
        rectangle "Pod 2" as P2 #lightgreen
        rectangle "Pod 3" as P3 #lightgreen
        rectangle "Grafana Alloy\n(DaemonSet)" as AL1 #orange
    }

    rectangle "Node 2" as N2 #lightblue {
        rectangle "Pod 4" as P4 #lightgreen
        rectangle "Pod 5" as P5 #lightgreen
        rectangle "Pod 6" as P6 #lightgreen
        rectangle "<PERSON>ana Alloy\n(DaemonSet)" as AL2 #orange
    }

    rectangle "Node 3" as N3 #lightblue {
        rectangle "Pod 7" as P7 #lightgreen
        rectangle "Pod 8" as P8 #lightgreen
        rectangle "Pod 9" as P9 #lightgreen
        rectangle "Grafana Alloy\n(DaemonSet)" as AL3 #orange
    }
}

' Storage and processing
rectangle "Loki\n(Log Aggregation)" as LOKI #yellow
rectangle "Grafana\nDashboard" as GRAF #lightblue
rectangle "Azure Monitor\nLogs" as AZURE #lightcoral

' Dedicated Persistent Volumes
package "Persistent Storage" {
    rectangle "Pod Logs PV\n(100GB)" as PODPV #lightcoral
    rectangle "Node Logs PV\n(50GB)" as NODEPV #lightcoral
    rectangle "Loki Storage PV\n(200GB)" as LOKIPV #lightcoral
}

' Log flows from pods to Grafana Alloy (GREEN = Pod Logs)
P1 -[#green,thickness=2]-> AL1 : "stdout/stderr logs"
P2 -[#green,thickness=2]-> AL1 : "stdout/stderr logs"
P3 -[#green,thickness=2]-> AL1 : "stdout/stderr logs"

P4 -[#green,thickness=2]-> AL2 : "stdout/stderr logs"
P5 -[#green,thickness=2]-> AL2 : "stdout/stderr logs"
P6 -[#green,thickness=2]-> AL2 : "stdout/stderr logs"

P7 -[#green,thickness=2]-> AL3 : "stdout/stderr logs"
P8 -[#green,thickness=2]-> AL3 : "stdout/stderr logs"
P9 -[#green,thickness=2]-> AL3 : "stdout/stderr logs"

' Node logs to Grafana Alloy (BLUE = System Logs)
N1 -[#blue,thickness=3]-> AL1 : "Node logs\n(/var/log)"
N2 -[#blue,thickness=3]-> AL2 : "Node logs\n(/var/log)"
N3 -[#blue,thickness=3]-> AL3 : "Node logs\n(/var/log)"

' Grafana Alloy to dedicated storage and Loki (PURPLE = Storage, ORANGE = Processing)
AL1 -[#purple,thickness=2]-> PODPV : "Pod logs storage"
AL2 -[#purple,thickness=2]-> PODPV : "Pod logs storage"
AL3 -[#purple,thickness=2]-> PODPV : "Pod logs storage"

AL1 -[#purple,thickness=2]-> NODEPV : "Node logs storage"
AL2 -[#purple,thickness=2]-> NODEPV : "Node logs storage"
AL3 -[#purple,thickness=2]-> NODEPV : "Node logs storage"

AL1 -[#orange,thickness=3]-> LOKI : "Parsed & enriched logs"
AL2 -[#orange,thickness=3]-> LOKI : "Parsed & enriched logs"
AL3 -[#orange,thickness=3]-> LOKI : "Parsed & enriched logs"

' Loki to storage and Grafana (PURPLE = Storage, YELLOW = Visualization, RED = Cloud)
LOKI -[#purple,thickness=4]-> LOKIPV : "Loki index storage"
LOKI -[#cyan,thickness=3]-> GRAF : "Log queries & visualization"
LOKI -[#red,thickness=3]-> AZURE : "Forward to Azure Monitor"

' Notes
note right of AL1
  **Grafana Alloy (Modern Collector)**
  - Replaces deprecated Promtail
  - Collects container & node logs
  - Parses and enriches with metadata
  - Forwards to Loki & storage
  - Can also collect metrics
end note

note right of LOKI
  - Log aggregation system
  - Prometheus-style for logs
  - Efficient storage
  - PromQL-like queries
end note

note right of GRAF
  - Unified log & metrics view
  - Search and filtering
  - Dashboard creation
  - Alerting integration
end note

note right of PODPV
  - Dedicated pod log storage
  - 100GB capacity
  - Retention policies
  - Backup enabled
end note

note right of NODEPV
  - Dedicated node log storage
  - 50GB capacity
  - System logs
  - Audit logs
end note

' Legend for logging flow
legend bottom right
  **Logging Flow Legend**
  |= Color |= Data Type |= Thickness |
  | <color:green>Green</color> | Pod Logs | 2 |
  | <color:blue>Blue</color> | Node/System Logs | 3 |
  | <color:orange>Orange</color> | Log Processing | 3 |
  | <color:purple>Purple</color> | Storage Ops | 2-4 |
  | <color:cyan>Cyan</color> | Visualization | 3 |
  | <color:red>Red</color> | Cloud Forward | 3 |
endlegend

@enduml
